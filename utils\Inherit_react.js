// import * as React from 'react';
// import { routerRedux } from 'dva/router';
// import Axios from 'axios';
// //包括 获取中间件、获取用户信息、带右边的头标题（包含隐藏头标题和加载头标题）
// window.setCallback = function (callback) {
//     window.callback = callback;
// };
// window.ReciveIp = function (ip) {//加载RN传过来的中间件IP
//     if (window.callback != undefined) {
//         window.callback.ReciveIp(ip);
//     }
// };
// window.BackUser = function (user) {//加载RN传过来的用户数据
//     if (window.callback != undefined) {
//         window.callback.BackUser(user);
//     }
// };
// window.Readinfor = function (information) {//加载RN传过来的HttpHeader信息
//     if (window.callback != undefined) {
//         window.callback.Readinfor(information);
//     }
// };
// window.PressRight = function () {//历史记录
//     if (window.callback != undefined) {
//         window.callback.PressRight();
//     }
// }
// window.KeyboardDidShow = function () {//键盘弹出
//     if (window.callback != undefined) {
//         window.callback.KeyboardDidShow();
//     }
// }
// window.KeyboardDidHide = function () {
//     if (window.callback != undefined) {
//         window.callback.KeyboardDidHide();
//     }
// }
// window.ReceiveParams = function (params) {//接收APP壳传过来的参数JSON
//     if (window.callback != undefined) {
//         window.callback.ReceiveParams(params);
//     }
// }

// ///基类封装
// ///by zm
// class Inherit extends React.Component {
//     constructor(props) {
//         super(props);
//         this.state = {};
//         this.window = window;
//         this.UserName = '';
//         this.UserCode = ''
//         if (process.env.NODE_ENV == 'development') {
//             this.UserCode = "SYS";
//             this.UserName = '系统';
//         }
//     }
//     //读取APP壳的HttpHeader信息
//     ReadHttpHeader() {
//         //定义好发送的JSON命令数据格式
//         let obj =
//         {
//             "type": "readdata",
//             "key": "HttpHeader",
//             "types": 1,
//             "backfunc": "Readinfor"
//         };
//         this.SendDataToDevice(obj);
//     }
//     //获取用户名
//     ReadUser() {
//         //定义好发送的JSON数据格式
//         let obj = {
//             type: "readuser",
//         };
//         this.SendDataToDevice(obj);
//     }
//     //读取APP壳的中间件服务器地址
//     ReadServerIp() {
//         //定义好发送的JSON命令数据格式
//         let obj =
//         {
//             "type": "readdata",
//             "key": "serverip",
//             "types": 1,
//             "backfunc": "ReciveIp"
//         };
//         this.SendDataToDevice(obj);
//     }
//     /**
//      * 从APP壳中获取所需数据(在componentDidMount中调用此方法)
//      * @param {*} othis 组件引用
//      */
//     initUser(othis) {
//         window.setCallback(othis);
//         this.ReadHttpHeader();
//         this.ReadUser(); //读取用户信息
//         this.ReadServerIp();//从APP壳读取中间件IP地址
//     }

//     //接收App壳传过来的用户信息
//     Readinfor = (information) => {
//         global.constants.codeto = information
//     };
//     //接收用户数据
//     BackUser = (user) => {
//         var data = user.split(",");
//         global.constants.userName = data[0];
//         //当前用户名
//         this.UserName = data[0];
//     };
//     //接收服务中间件IP
//     ReciveIp = (ip) => {
//         global.constants.serverip = ip;
//     };

//     //显示导航栏二级页面通过壳打开
//     ShowRightText(text, callback) {
//         let obj = {
//             type: "showrighttext",
//             state: 0,
//             right_url: "",
//             callfunc: callback,
//             right_text: text,
//         };
//         this.SendDataToDevice(obj);
//     }

//     /**
//     * 跳转到二级页面（如：历史记录）
//     * @param {*} title 页面标题
//     * @param {*} url 页面路径
//     */
//     goToSecondPage(title, url) {
//         let msg = {
//             type: "goToWeb2",
//             title: title,
//             url: url,
//         };
//         this.SendDataToDevice(msg);
//     }

//     /**
//     * 跳转到三级页面（如：详细记录）
//     * @param {*} title 页面标题
//     * @param {*} url 页面路径
//     */
//     goToThirdPage(title, url) {
//         let msg = {
//             type: "goToWeb3",
//             title: title,
//             url: url,
//         };
//         this.SendDataToDevice(msg);
//     }

//     /**
//    * 向本地写入数据（数据持久化）
//    * @param {string} key 数据存储的key值
//    * @param {string} value 数据
//    */
//     WriteToLocal(key, value) {
//         var obj = {
//             type: "writedata",
//             key: key,
//             value: value,
//             valuetype: "1",
//         };
//         this.SendDataToDevice(obj);
//     }

//     /**
//      * 从本地读取数据（数据持久化）
//      * @param {string} key
//      * @param {string} callback 返回的回调函数名称
//      * callback举例-->ReadDataCallback(value){}
//      */
//     ReadFromLocal(key, callback) {
//         var obj = {
//             type: "readdata",
//             key: key,
//             types: 1,
//             backfunc: callback,
//         };
//         this.SendDataToDevice(obj);
//     }


//     //跳转封装，url要跳转的地址，json传的对象(pc端使用)
//     RedirectTo(url, json = {}) {
//         var path = this.props.router.location.pathname;
//         if (path.indexOf('?') >= 0) {
//             path = '/';
//         }
//         console.log("---RedirectTo--,url", url, path,json);
//         this.props.dispatch(routerRedux.push({
//             pathname: path + '?' + url,
//             json: JSON.stringify(json)
//         }))
//     }

//     //设置横屏
//     lockToLandscape() {
//         let obj = {
//             type: 'lockToLandscape'
//         }
//         this.SendDataToDevice(obj);
//     }

//     /**
//      * PDA页面加载自动聚焦到第一个
//      * 输入框，在调用this.xxInput.focus()
//      * 前需要先调用这个方法
//      * callback 回调方法
//      */
//     RequestFocus(callback) {
//         let obj = {
//             type: "requestFocus",
//         };
//         this.SendDataToDevice(obj);
//         if (callback) {
//             callback();
//         }
//     }
//     /**
//      * 阻止键盘弹出
//      * @param {*} id 元素id
//      */
//     StopKeyBoard(id) {
//         document.getElementById(id).setAttribute("readonly", true);
//         setTimeout(() => {
//             document.getElementById(id).removeAttribute("readonly");
//         }, 200);
//     }
//     /**
//      * 返回上一页
//      */
//     goBack() {
//         let msg = {
//             type: "goBack"
//         }
//         this.SendDataToDevice(msg);
//     }
//     /**
//      * 刷新前一个页面
//      */
//     refrehFront() {
//         let msg = {
//             type: "refreshFront"
//         }
//         this.SendDataToDevice(msg);
//     }

//     //地址栏参数获取
//     urlGetParam(param1) {
//         let pathurl = this.props.router.location.search;
//         let pathdata = pathurl.substring(pathurl.indexOf('=') + 1, pathurl.length);
//         if (pathdata != "" && pathdata != undefined) {
//             if (pathdata.indexOf('/') <= 0) {
//                 localStorage.setItem(param1, pathdata);
//             }
//         }
//         return pathdata;
//     }

//     /**
//      * 推送消息（走APP推送）
//      * @param {*} message 消息体
//      */
//     PushMessage(message) {
//         let msg = {
//             type: "PushMessage",
//             message
//         }
//         //不支持iOS推送
//         if (navigator.platform.toString().toLowerCase().indexOf('linux') >= 0) {
//             window.ReactNativeWebView.postMessage(JSON.stringify(JSON.stringify(msg)));
//         }
//     }

//     /**
//      * 推送企业微信消息
//      * @param {*} touser 推送用户 
//      * @param {*} message 消息内容
//      */
//     PushWXMessage(touser, message) {
//         let formData = {
//             touser: touser,
//             msgtype: "text",
//             content: message
//         }
//         let url = "http://192.168.1.247:8089/http/sendMessage";
//         Axios.post(url, formData).then(response => {
//             return response.data;
//         }).then(result => {
//             console.log("--PushWXMessage--", result);
//         })
//     }


//     //扫描二维码 设备
//     Scan = (index) => {
//         // scanindex = index;
//         let obj = {
//             "type": "scanbarcode"
//         };
//         this.SendDataToDevice(obj);
//     }

//     //向APP壳发送命令请求原生设备的数据
//     SendDataToDevice(obj) {
//         //在PDA才能调用该函数
//         let platform = navigator.platform.toString().toLowerCase();
//         if (platform.indexOf('linux') >= 0) {
//             //安卓设备
//             window.ReactNativeWebView.postMessage(JSON.stringify(JSON.stringify(obj)));
//         } else if (platform.indexOf('iphone') >= 0) {
//             //iOS设备
//             window.webkit.messageHandlers.ReactNativeWebView.postMessage(obj);
//         }
//     }

// }
// export default Inherit;