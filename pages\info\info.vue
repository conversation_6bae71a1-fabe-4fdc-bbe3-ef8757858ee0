<template>

	<view>
		<uni-nav-bar status-bar :fixed="true" :border="false">
			<view class="flex fontSize-34  w-full  justify-center items-center">消息</view>
			<block v-slot:right>
				<uni-icons @click="toggleMenu()" type="more-filled" size="30"></uni-icons>
			</block>
		</uni-nav-bar>
		<view :style="{ height: `${windowBodyHeight}px` }" class="flex bg-white w-full">
			<view class="flex justify-between  w-full flex-col">
				<!-- 分类 -->
				<u-sticky style="background-color: #efeff4;" class="p-all-20  " offsetTop="0">
					<!-- <uni-easyinput suffixIcon="search" placeholder="搜索应用" @iconClick="iconClick"></uni-easyinput> -->
					<uni-easyinput prefixIcon="search" @change="searchAppClick" @clear="searchAppClick"
						v-model="searchInput" placeholder="搜索应用" @iconClick="searchAppClick"></uni-easyinput>
				</u-sticky>
				<!-- 列表内容 -->
				<scroll-view scroll-y="true" @scroll="onScroll" class="flex-1 overflow-y-auto">
					<view class="  flex  flex-col pl-20 pr-20 ">

						<block v-if="messageList">
											<uni-list class="mb-80">
												<uni-list :border="true">
												
													<uni-list-chat @click="navigateTo('/pages/common/common', {})" :clickable="true" title="第一条数据" avatar="https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/unicloudlogo.png" note="您收到一条新的消息" time="2020-02-02 20:20" badge-text="12"></uni-list-chat>
													<!-- 头像显示圆点 -->
													<uni-list-chat title="uni-app" avatar="https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/unicloudlogo.png" note="您收到一条新的消息" time="2020-02-02 20:20" badge-positon="left" badge-text="dot"></uni-list-chat>
													<!-- 头像显示角标 -->
													<uni-list-chat title="uni-app" avatar="https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/unicloudlogo.png" note="您收到一条新的消息" time="2020-02-02 20:20" badge-positon="left" badge-text="99"></uni-list-chat>
													<!-- 显示多头像 -->
													<uni-list-chat title="uni-app" :avatar-list="avatarList" note="您收到一条新的消息" time="2020-02-02 20:20" badge-positon="left" badge-text="dot"></uni-list-chat>
													<!-- 自定义右侧内容 -->
													<uni-list-chat title="uni-app" :avatar-list="avatarList" note="您收到一条新的消息" time="2020-02-02 20:20" badge-positon="left" badge-text="dot">
														<view class="chat-custom-right">
															<text class="chat-custom-text">刚刚</text>
															<!-- 需要使用 uni-icons 请自行引入 -->
															<uni-icons type="star-filled" color="#999" size="18"></uni-icons>
														</view>
													</uni-list-chat>
													<!-- 显示圆形头像 -->
													<uni-list-chat :avatar-circle="true" title="uni-app" avatar="https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/unicloudlogo.png" note="您收到一条新的消息" time="2020-02-02 20:20" ></uni-list-chat>
													<!-- 右侧带角标 -->
													<uni-list-chat title="uni-app" avatar="https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/unicloudlogo.png" note="您收到一条新的消息" time="2020-02-02 20:20" badge-text="12"></uni-list-chat>
													<!-- 头像显示圆点 -->
													<uni-list-chat title="uni-app" avatar="https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/unicloudlogo.png" note="您收到一条新的消息" time="2020-02-02 20:20" badge-positon="left" badge-text="dot"></uni-list-chat>
													<!-- 头像显示角标 -->
													<uni-list-chat title="uni-app" avatar="https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/unicloudlogo.png" note="您收到一条新的消息" time="2020-02-02 20:20" badge-positon="left" badge-text="99"></uni-list-chat>
													<!-- 显示多头像 -->
													<uni-list-chat title="uni-app" :avatar-list="avatarList" note="您收到一条新的消息" time="2020-02-02 20:20" badge-positon="left" badge-text="dot"></uni-list-chat>
													<!-- 自定义右侧内容 -->
													<uni-list-chat title="uni-app" :avatar-list="avatarList" note="您收到一条新的消息" time="2020-02-02 20:20" badge-positon="left" badge-text="dot">
														<view class="chat-custom-right">
															<text class="chat-custom-text">刚刚</text>
															<!-- 需要使用 uni-icons 请自行引入 -->
															<uni-icons type="star-filled" color="#999" size="18"></uni-icons>
														</view>
													</uni-list-chat>
													<!-- 显示圆形头像 -->
													<uni-list-chat :avatar-circle="true" title="uni-app" avatar="https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/unicloudlogo.png" note="您收到一条新的消息" time="2020-02-02 20:20" ></uni-list-chat>
													<!-- 右侧带角标 -->
													<uni-list-chat title="uni-app" avatar="https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/unicloudlogo.png" note="您收到一条新的消息" time="2020-02-02 20:20" badge-text="12"></uni-list-chat>
													<!-- 头像显示圆点 -->
													<uni-list-chat title="uni-app" avatar="https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/unicloudlogo.png" note="您收到一条新的消息" time="2020-02-02 20:20" badge-positon="left" badge-text="dot"></uni-list-chat>
													<!-- 头像显示角标 -->
													<uni-list-chat title="uni-app" avatar="https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/unicloudlogo.png" note="您收到一条新的消息" time="2020-02-02 20:20" badge-positon="left" badge-text="99"></uni-list-chat>
													<!-- 显示多头像 -->
													<uni-list-chat title="uni-app" :avatar-list="avatarList" note="您收到一条新的消息" time="2020-02-02 20:20" badge-positon="left" badge-text="dot"></uni-list-chat>
													<!-- 自定义右侧内容 -->
													<uni-list-chat title="uni-app" :avatar-list="avatarList" note="您收到一条新的消息" time="2020-02-02 20:20" badge-positon="left" badge-text="dot">
														<view class="chat-custom-right">
															<text class="chat-custom-text">刚刚</text>
															<!-- 需要使用 uni-icons 请自行引入 -->
															<uni-icons type="star-filled" color="#999" size="18"></uni-icons>
														</view>
													</uni-list-chat>
													<!-- 显示圆形头像 -->
													<uni-list-chat :avatar-circle="true" title="uni-app" avatar="https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/unicloudlogo.png" note="您收到一条新的消息" time="2020-02-02 20:20" ></uni-list-chat>
													<!-- 右侧带角标 -->
													<uni-list-chat title="uni-app" avatar="https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/unicloudlogo.png" note="您收到一条新的消息" time="2020-02-02 20:20" badge-text="12"></uni-list-chat>
													<!-- 头像显示圆点 -->
													<uni-list-chat title="uni-app" avatar="https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/unicloudlogo.png" note="您收到一条新的消息" time="2020-02-02 20:20" badge-positon="left" badge-text="dot"></uni-list-chat>
													<!-- 头像显示角标 -->
													<uni-list-chat title="uni-app" avatar="https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/unicloudlogo.png" note="您收到一条新的消息" time="2020-02-02 20:20" badge-positon="left" badge-text="99"></uni-list-chat>
													<!-- 显示多头像 -->
													<uni-list-chat title="uni-app" :avatar-list="avatarList" note="您收到一条新的消息" time="2020-02-02 20:20" badge-positon="left" badge-text="dot"></uni-list-chat>
													<!-- 自定义右侧内容 -->
													<uni-list-chat title="最后一条" :avatar-list="avatarList" note="您收到一条新的消息" time="2020-02-02 20:20" badge-positon="left" badge-text="dot">
														<view class="chat-custom-right">
															<text class="chat-custom-text">刚刚-最后一条</text>
															<!-- 需要使用 uni-icons 请自行引入 -->
															<uni-icons type="star-filled" color="#999" size="18"></uni-icons>
														</view>
													</uni-list-chat>
												</uni-list>
											</uni-list>
										</block>
										<block v-else>
											<view class="flex justify-center items-center h-full ">
												暂无数据
											</view>
											
										</block>


					</view>
				</scroll-view>
			</view>
		</view>
	</view>
</template>

<script setup>
import * as serviceApi from '@/api/index.js'
import { onPullDownRefresh } from '@dcloudio/uni-app';
import { getCurrentUserInfoByField } from '@/utils/tools.js'
import {
	ref,
	onMounted,
	computed
} from 'vue'
import {
	setStorageSync,
	getStorageSync,
	CURRENT_SERVER
} from '@/utils/Storage.js'
const isOpenAllCollapse = ref(true) // 是否所有折叠
const messageList = ref(99)
const navBarHeight = ref(44) // 顶部标题高度
const tabBarHeight = ref(50) // 底部菜单栏高度	
const isEdit = ref(false)
const currentLongPressTab = ref(null)
const menuCategory = ref([])
const searchInput = ref('')
const copyMenuCategory = ref([])
const systemInfo = uni.getSystemInfoSync(); //系统信息
const windowHeight = computed(() => {
	//windowHeight不包含NavigationBar和TabBar的高度
	return systemInfo.windowHeight
})
const windowBodyHeight = computed(() => {
	//windowHeight不包含NavigationBar和TabBar的高度
	return systemInfo.windowHeight - navBarHeight.value - systemInfo.safeArea.top
})

// setTimeout(()=>{
// 	uni.showTabBarRedDot({
// 		  index: 0
// 	})
// },1000)
onMounted(() => {
	checkMenuDataBeforeLoad()
})

// 下拉刷新处理函数
onPullDownRefresh(async () => {
	console.log('下拉刷新处理函数');
	try {
		// 重新加载数据
		loadMenuList()

		// 停止刷新动画（必须调用）
		setTimeout(function () {
			uni.stopPullDownRefresh({
				// success: function () {
				// 	uni.showToast({
				// 		title:'刷新成功！'
				// 	})
				// },
				// fail: function () {
				// 	uni.showToast({
				// 		title:'刷新失败！'
				// 	})
				// },
				// complete: function () {
				// 	uni.showToast({
				// 		title:'刷新完成！'
				// 	})
				// }
			});
		}, 1000);
	} catch (error) {
		console.error('刷新失败:', error);
		setTimeout(function () {
			// 失败时也要停止动画
			uni.stopPullDownRefresh();
		}, 1000);
	}
});
//////////////////////methods//////////////////////////
function checkMenuDataBeforeLoad() {
	let _ModuleData = getStorageSync('ModuleData')
	if (_ModuleData) {
		handleMenuData(_ModuleData)
	} else {
		loadMenuList()
	}
}
function handleMenuData(menuDataList = []) {
	let _menuModulesData = menuDataList.ModuleTrees.filter(item => item.CBEHAVIOR_PATH == 'App')
	if (_menuModulesData && _menuModulesData.length > 0) {
		menuCategory.value = _menuModulesData[0].Children
		copyMenuCategory.value = JSON.parse(JSON.stringify(menuCategory.value))
	}
}
const isScrolling = ref(false)
const scrollTimer = ref(null)
function onScroll() {
	isScrolling.value = true;
	// 2. 清除之前的计时器（避免多次触发时计时混乱）
	if (scrollTimer.value) {
		clearTimeout(scrollTimer.value);
	}

	// 3. 启动新计时器：300ms 后若没有再滚动，视为滚动结束
	scrollTimer.value = setTimeout(() => {
		isScrolling.value = false; // 标记滚动结束
		scrollTimer.value = null; // 清空计时器
		console.log('isScrolling.value:', isScrolling.value)
	}, 500);
}

// 加载菜单数据
function loadMenuList() {
	const params = {
		username: getCurrentUserInfoByField('CUSER_NAME'),
		systemtype: 'APP'
	}
	uni.showLoading({
		title: '加载中'
	});
	serviceApi.GetModulesByUser(null, params).then(res => {
		if (res && res.data.code === 200 && res.data.data.Success) {
			setStorageSync('ModuleData', res.data.data.Datas)
			handleMenuData(res.data.data.Datas)
		} else {
			uni.showToast({
				title: res && res.data.data.Content ? res.data.data.Content : '获取菜单信息失败',
				icon: 'none'
			})
		}
	}).catch(err => {
		uni.showToast({
			title: '网络异常，请重试',
			icon: 'none'
		})
	}).finally(() => {
		console.log("finally")
		uni.hideLoading();
	})
}


// 应用搜索
function searchAppClick() {
	let val = searchInput.value
	if (val) {
		let _tempData = JSON.parse(JSON.stringify(copyMenuCategory.value))
		// 过滤的数据是 item.Children 的数据
		menuCategory.value = _tempData.map(item => {
			item.Children = item.Children.filter(child => child.CMODULE_NAME.includes(val))
			return item
		}).filter(item => item.Children.length > 0)

	} else {
		menuCategory.value = JSON.parse(JSON.stringify(copyMenuCategory.value))
	}
}
// 保留当前页面，跳转到应用内的某个页面，使用uni.navigateBack可以返回到原页面。
function navigateTo(url, params) {
	console.log("navigateTo===params===", JSON.stringify(params))
	if(!params.text){
		params.text = "测试"
	}
	//在起始页面跳转到test.vue页面并传递参数
	uni.navigateTo({
		url: url,//'test?id=1&name=uniapp'
		success: function (res) {
			// 通过eventChannel向被打开页面传送数据
			res.eventChannel.emit('acceptDataFromOpenerPage', params)
		}
	});
}
function toggleMenu(type) {
		uni.showActionSheet({
			itemList: ['全部已读', '全部删除'],
			success: function(res) {
				console.log('选中了第' + (res.tapIndex + 1) + '个按钮');
			},
			fail: function(res) {
				console.log(res.errMsg);
			}
		});
	}

function longpressItem(index) {
	console.log('isScrolling.value:', isScrolling.value)
	if (!isScrolling.value) { // 只有不在滚动时，才执行长按逻辑
		currentLongPressTab.value = index
	}
}

function change_Collapse() {
	currentLongPressTab.value = null
}
</script>


<style lang="scss">
.imageItem {
	width: 60rpx;
	height: 60rpx;
}

.textDesc {
	font-size: 24rpx;
	margin-top: 10rpx;
	color: #363636; //#8c8c8c;
}
</style>