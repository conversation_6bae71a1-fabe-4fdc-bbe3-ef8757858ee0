
  ;(function(){
  let u=void 0,isReady=false,onReadyCallbacks=[],isServiceReady=false,onServiceReadyCallbacks=[];
  const __uniConfig = {"pages":[],"globalStyle":{"backgroundColor":"#F8F8F8","background":"#efeff4","navigationBar":{"backgroundColor":"#F8F8F8","titleText":"uni-app","type":"default","titleColor":"#000000"},"isNVue":false},"nvue":{"compiler":"uni-app","styleCompiler":"uni-app","flex-direction":"column"},"renderer":"auto","appname":"CIMOM-UNI-APP","splashscreen":{"alwaysShowBeforeRender":true,"autoclose":true},"compilerVersion":"4.57","entryPagePath":"pages/login/login","entryPageQuery":"","realEntryPagePath":"","networkTimeout":{"request":60000,"connectSocket":60000,"uploadFile":60000,"downloadFile":60000},"tabBar":{"position":"bottom","color":"#000000","selectedColor":"#000000","borderStyle":"black","blurEffect":"none","fontSize":"10px","iconWidth":"24px","spacing":"3px","height":"50px","list":[{"pagePath":"pages/home/<USER>","iconPath":"/static/images/tabbar/home.png","selectedIconPath":"/static/images/tabbar/home_.png","text":"首页"},{"pagePath":"pages/menus/menus","iconPath":"/static/images/tabbar/work.png","selectedIconPath":"/static/images/tabbar/work_.png","text":"应用"},{"pagePath":"pages/work/work","iconPath":"/static/images/tabbar/ic_workbench.png","selectedIconPath":"/static/images/tabbar/ic_workbench_.png","text":"工作台"},{"pagePath":"pages/info/info","iconPath":"/static/images/tabbar/message.png","selectedIconPath":"/static/images/tabbar/message_.png","text":"消息"},{"pagePath":"pages/mine/mine","iconPath":"/static/images/tabbar/mine.png","selectedIconPath":"/static/images/tabbar/mine_.png","text":"我的"}],"backgroundColor":"#f8f8f8","selectedIndex":0,"shown":true},"fallbackLocale":"zh-Hans","locales":{},"darkmode":false,"themeConfig":{}};
  const __uniRoutes = [{"path":"pages/login/login","meta":{"isQuit":true,"isEntry":true,"titleView":false,"navigationBar":{"titleText":"登录","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/servers/servers","meta":{"navigationBar":{"titleText":"服务器设置","type":"default"},"isNVue":false}},{"path":"pages/home/<USER>","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":0,"navigationBar":{"titleText":"首页","type":"default"},"isNVue":false}},{"path":"pages/mine/mine","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":4,"navigationBar":{"titleText":"我的","type":"default"},"isNVue":false}},{"path":"pages/menus/menus","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":1,"enablePullDownRefresh":true,"navigationBar":{"titleText":"应用","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/info/info","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":3,"enablePullDownRefresh":true,"navigationBar":{"titleText":"消息","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/mine/info","meta":{"navigationBar":{"titleText":"个人信息","type":"default"},"isNVue":false}},{"path":"pages/mine/about","meta":{"navigationBar":{"titleText":"关于APP","type":"default"},"isNVue":false}},{"path":"pages/mine/password","meta":{"navigationBar":{"titleText":"修改密码","type":"default"},"isNVue":false}},{"path":"pages/work/work","meta":{"isQuit":true,"isTabBar":true,"tabBarIndex":2,"enablePullDownRefresh":true,"navigationBar":{"titleText":"工作台","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/mine/log","meta":{"navigationBar":{"titleText":"日志","type":"default"},"isNVue":false}},{"path":"pages/mine/log-login","meta":{"navigationBar":{"titleText":"登录日志","type":"default"},"isNVue":false}},{"path":"pages/mine/log-service","meta":{"navigationBar":{"titleText":"服务日志","type":"default"},"isNVue":false}},{"path":"pages/mine/log-debgger","meta":{"navigationBar":{"titleText":"调试日志","type":"default"},"isNVue":false}},{"path":"pages/mine/aboutPhone","meta":{"navigationBar":{"titleText":"本机信息","type":"default"},"isNVue":false}},{"path":"pages/common/common","meta":{"navigationBar":{"titleText":"通用页面Webview","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/common/secondPage","meta":{"navigationBar":{"titleText":"通用页面Webview-secondPage","style":"custom","type":"default"},"isNVue":false}},{"path":"pages/blueTeeth/blueTeeth","meta":{"navigationBar":{"titleText":"蓝牙","type":"default"},"isNVue":false}},{"path":"pages/locationCheckIn/locationCheckIn","meta":{"navigationBar":{"titleText":"定位打卡","type":"default"},"isNVue":false}},{"path":"pages/mqttToolTest/mqttToolTest","meta":{"navigationBar":{"titleText":"消息推送测试","type":"default"},"isNVue":false}}].map(uniRoute=>(uniRoute.meta.route=uniRoute.path,__uniConfig.pages.push(uniRoute.path),uniRoute.path='/'+uniRoute.path,uniRoute));
  __uniConfig.styles=[];//styles
  __uniConfig.onReady=function(callback){if(__uniConfig.ready){callback()}else{onReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"ready",{get:function(){return isReady},set:function(val){isReady=val;if(!isReady){return}const callbacks=onReadyCallbacks.slice(0);onReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
  __uniConfig.onServiceReady=function(callback){if(__uniConfig.serviceReady){callback()}else{onServiceReadyCallbacks.push(callback)}};Object.defineProperty(__uniConfig,"serviceReady",{get:function(){return isServiceReady},set:function(val){isServiceReady=val;if(!isServiceReady){return}const callbacks=onServiceReadyCallbacks.slice(0);onServiceReadyCallbacks.length=0;callbacks.forEach(function(callback){callback()})}});
  service.register("uni-app-config",{create(a,b,c){if(!__uniConfig.viewport){var d=b.weex.config.env.scale,e=b.weex.config.env.deviceWidth,f=Math.ceil(e/d);Object.assign(__uniConfig,{viewport:f,defaultFontSize:16})}return{instance:{__uniConfig:__uniConfig,__uniRoutes:__uniRoutes,global:u,window:u,document:u,frames:u,self:u,location:u,navigator:u,localStorage:u,history:u,Caches:u,screen:u,alert:u,confirm:u,prompt:u,fetch:u,XMLHttpRequest:u,WebSocket:u,webkit:u,print:u}}}}); 
  })();
  