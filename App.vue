<script>
	import {
		setStorageSync,
		getStorageSync,
		CURRENT_SERVER
	} from '@/utils/Storage.js'
	export default {
		onLaunch: function() {
			// 测试提交代码加密
			console.warn('当前组件仅支持 uni_modules 目录结构 ，请升级 HBuilderX 到 3.1.0 版本以上！')
			console.log('App Launch')
			// 检查是否支持 uni - push https://zh.uniapp.dcloud.io/api/plugins/provider.html
			// {
			//     "service": "push",
			//     "provider": [
			//         "unipush"
			//     ],
			//     "providers": [
			//         {
			//             "id": "unipush",
			//             "token": "23a6d48e7589a927de0905f9d2624481",
			//             "clientid": "23a6d48e7589a927de0905f9d2624481",
			//             "appid": "pPyZWvH3Fa6PXba10aJ009",
			//             "appkey": "b7dOGlNPHR7pqwUxDhpTi4"
			//         }
			//     ],
			//     "errMsg": "getProvider:ok"
			// }
			//////复制当前用户clientid~////
			// uni.getProvider({
			// 	service: 'push',
			// 	success: function (res) {
			// 		 //return
			// 		  console.log('检查是否支持push成功:', res);
			// 		  console.log('~~~~当前用户clientid~1',res.provider.includes('unipush'));
			// 		if (res.provider.includes('unipush')) {
			// 			 console.log('~~~~当前用户clientid~2');
			// 			 uni.showModal({
			// 			 	title: '提示',
			// 			 	content:'当前clientid:'+res.providers[0].clientid,
			// 			 	success: function (btn) {
			// 			 		if (btn.confirm) {
			// 							console.log('用户点击确定');
			// 							uni.setClipboardData({
			// 										data: res.providers[0].clientid,
			// 										success: function () {
			// 											uni.showToast({
			// 												title:'复制 成功!'
			// 											})
			// 										}
			// 								});
			// 						} else if (btn.cancel) {
			// 			 			console.log('用户点击取消');
			// 			 		}
			// 			 	}
			// 			 });
						
			// 			// 			// 可以将 clientid 发送到服务器，用于后续推送
			// 			// 			  // 示例：将 clientid 发送到服务器
			// 			// 			  // uni.request({
			// 			// 			  //     url: 'https://your-server-url.com/api/saveClientId',
			// 			// 			  //     method: 'POST',
			// 			// 			  //     data: {
			// 			// 			  //         clientid: res.clientid
			// 			// 			  //     },
			// 			// 			  //     success: (serverRes) => {
			// 			// 			  //         console.log('客户端 ID 发送到服务器成功:', serverRes);
			// 			// 			  //     },
			// 			// 			  //     fail: (err) => {
			// 			// 			  //         console.log('客户端 ID 发送到服务器失败:', err);
			// 			// 			  //     }
			// 			// 			  // });  
			// 		}
			// 	},
			// 	fail:function (err) {
			// 		 console.log('检查是否支持push失败:', err);
			// 	}
			// });
			//////复制当前用户clientid~////
			//console.log('检查是否支持:',uni.getProvider({service: 'push'}).provider)
			// if (uni.getProvider({service: 'push'}).provider.length > 0) {
			// 	// 初始化推送
			// 	uni.getPushClientId({
			// 		success: function(res) {
			// 			console.log('获取客户端 ID 成功:', res.clientid);
		              
			// 		},
			// 		fail: function(err) {
			// 			console.log('获取客户端 ID 失败:', err);
			// 		}
			// 	});
			// } else {
			// 	console.log('当前环境不支持 uni - push');
			// }
		},
		onShow: function() {
			console.log('App Show')
			let _ModuleData = getStorageSync('ModuleData')
			//debugger
			if(_ModuleData && _ModuleData.ModuleTrees){
					let showHomeIndex = _ModuleData.ModuleTrees.findIndex(item=>item.CBEHAVIOR_PATH=='Home')
					let showWorkIndex = _ModuleData.ModuleTrees.findIndex(item=>item.CBEHAVIOR_PATH=='Work')
					let showMessageIndex = _ModuleData.ModuleTrees.findIndex(item=>item.CBEHAVIOR_PATH=='Message')
				
				setTimeout(()=>{
					if(showHomeIndex==-1){
						uni.setTabBarItem({
							index:0,
							visible:false,
							
						})
					}
					if(showWorkIndex==-1){
							uni.setTabBarItem({
							index:2,
							visible:false,
							})
					}
					if(showMessageIndex==-1){
							uni.setTabBarItem({
							index:3,
							visible:false,
							})
					}
				},1000)	
			}
			// 监听推送消息
			uni.onPushMessage((res) => {
				console.log('收到推送消息:', res);
				// 处理推送消息
				if (res.type === 'receive') {
					// 接收到通知消息
					const message = res.content;
					// 可以在这里进行消息展示或其他处理
					// 如 uni.showToast 等方法展示消息
					uni.showToast({
						title: '收到新消息',
						icon: 'none',
						duration: 3000
					});
				} else if (res.type === 'click') {
					// 用户点击通知消息
					console.log('用户点击了通知消息');
					// 可以在这里处理点击通知后的操作，如跳转到指定页面
					// uni.navigateTo({
					//     url: '/pages/detail/detail'
					// });
				}
			});
		},
		onHide: function() {
			console.log('App Hide')
		},
		onUnload() {
			// 取消监听推送消息
			uni.offPushMessage();
		},
		
	}
</script>

<style lang="scss">
	/*每个页面公共Flex css 元样式 */
	@import './baseFlex.scss';
	// 字体样式 iconfont
	@import "@/static/font/iconfont.css";
	// 字体样式 iconfont1
	@import "@/static/ifont/iconfont.css";
	/*每个页面公共css */
	@import '@/uni_modules/uni-scss/index.scss';
	/* #ifndef APP-NVUE */
	@import '@/static/customicons.css';

	// 设置整个项目的背景色
	page {
		//background-color: #f5f5f5; // 预设
		background-color: #efeff4; // 自定义颜色 by andy
	}

	/* #endif */
	// .example-info {
	// 	font-size: 14px;
	// 	color: #333;
	// 	padding: 10px;
	// }
</style>