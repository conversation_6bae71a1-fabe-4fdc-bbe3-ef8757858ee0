/**
 * 参考文档
 * https://www.quanzhan.co/luch-request/guide/3.x/#%E5%8F%AF%E9%85%8D%E7%BD%AE%E9%A1%B9
 */
import {
  http
} from '@/utils/axios.js'

/**
 * 查询商品列表
 * @param {Object} params - 查询参数  
 */
export const getGoodsList = (params) => {
  return http.get('/api/user/list', {
    params
  })
}

// 通用请求方法middleware 演示。文档：https://www.quanzhan.co/luch-request/guide/3.x/#middleware
/**
 * 查询商品信息
 * @param {Object} data - 查询数据
 * @param {Object} params - 查询params参数
 */
export const getGoodsInfo = (data, params) => {
  return http.middleware({
    method: 'POST', // 必须大写 请求方法必须大写 [GET|POST|PUT|DELETE|CONNECT|HEAD|OPTIONS|TRACE]
    url: '/api/user/update',
    data: data,
    params: params,
    custom: {
      auth: true
    }
  })
}

export const downLoadField = (data,params) =>{
	// 具体参数说明：[uni.downloadFile](https://uniapp.dcloud.io/api/request/network-file?id=downloadfile)
	  http.download('api/download', {
	    // baseURL: '',
	    params: {}, /* 会加在url上 */
	    // #ifdef H5 || APP-PLUS
	    timeout: 3000, // H5(HBuilderX 2.9.9+)、APP(HBuilderX 2.9.9+)
	    // #endif
	    header: {}, /* 会与全局header合并，如有同名属性，局部覆盖全局 */
	    custom: {}, // 自定义参数
	    // 返回当前请求的task, options。非必填
	    getTask: (task, options) => {
	      // setTimeout(() => {
	      //   task.abort()
	      // }, 500)
	    },
	    //validateStatus: (statusCode) => { // statusCode 必存在。此处示例为全局默认配置。演示，非必填选项
	   	//	return statusCode >= 200 && statusCode < 300
	   	//},
	     // 自定义处理params 函数
	     //paramsSerializer: (params) => {
	     //   return qs.stringify(params)
	     //}
	  }).then(res => {
	
	  }).catch(err => {
	
	  })
} 
export const uploadImg = (data,params) =>{
	 // 具体参数说明：[uni.uploadFile](https://uniapp.dcloud.io/api/request/network-file)
	  http.upload('api/upload/img', {
	    // baseURL: '',
	    params: {}, /* 会加在url上 */
	    // #ifdef APP-PLUS || H5
	    files: [], // 需要上传的文件列表。使用 files 时，filePath 和 name 不生效。App、H5（ 2.6.15+）
	    // #endif
	    // #ifdef MP-ALIPAY
	    fileType: 'image/video/audio', // 仅支付宝小程序，且必填。
	    // #endif
	    filePath: '', // 要上传文件资源的路径。
	    name: 'file', // 文件对应的 key , 开发者在服务器端通过这个 key 可以获取到文件二进制内容
	    // #ifdef H5 || APP-PLUS
	    timeout: 3000, // H5(HBuilderX 2.9.9+)、APP(HBuilderX 2.9.9+)
	    // #endif
	    header: {}, /* 会与全局header合并，如有同名属性，局部覆盖全局 */
	    custom: {}, // 自定义参数
	    formData: {}, // HTTP 请求中其他额外的 form data
	    // 返回当前请求的task, options。请勿在此处修改options。非必填
	    getTask: (task, options) => {
	      // setTimeout(() => {
	      //   task.abort()
	      // }, 500)
	    },
	    //validateStatus: (statusCode) => { // statusCode 必存在。此处示例为全局默认配置。演示，非必填选项
	    //	return statusCode >= 200 && statusCode < 300
	    //},
	     // 自定义处理params 函数
	     //paramsSerializer: (params) => {
	     //   return qs.stringify(params)
	     //}
	  }).then(res => {
	    // 返回的res.data 已经进行JSON.parse
	  }).catch(err => {
	
	  })
}

export const getList = (data,params) =>{
	http.request({
	    // baseURL: '',
	    method: 'POST', // 请求方法必须大写 [GET|POST|PUT|DELETE|CONNECT|HEAD|OPTIONS|TRACE]
	    url: '/user/12345',
	    data: {
	      firstName: 'Fred',
	      lastName: 'Flintstone'
	    },
	    // #ifdef H5 || APP-PLUS || MP-ALIPAY || MP-WEIXIN
	    timeout: 60000, // H5(HBuilderX 2.9.9+)、APP(HBuilderX 2.9.9+)、微信小程序（2.10.0）、支付宝小程序
	    // #endif
	    params: { // 会拼接到url上
	      token: '1111'
	    },
	    // #ifdef APP-PLUS
	    firstIpv4: false, // DNS解析时优先使用ipv4 仅 App-Android 支持 (HBuilderX 2.8.0+)
	    // #endif
	    // 注：如果局部custom与全局custom有同名属性，则后面的属性会覆盖前面的属性，相当于Object.assign(全局，局部)
	    custom: {}, // 自定义参数
	    // 返回当前请求的task, options。请勿在此处修改options。非必填
	    getTask: (task, options) => {
	      // setTimeout(() => {
	      //   task.abort()
	      // }, 500)
	    },
	    //validateStatus: (statusCode) => { // statusCode 必存在。此处示例为全局默认配置。演示，非必填选项
	    //	return statusCode >= 200 && statusCode < 300
	    //},
	     // 自定义处理params 函数
	     //paramsSerializer: (params) => {
	     //   return qs.stringify(params)
	     //}
	  })
	
}