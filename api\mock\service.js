import {
  axios
} from '@/utils/axios.js'
export const login = (data, params) => {
  return  axios.request({
    // baseURL: '',
    method: 'POST', // 请求方法必须大写 [GET|POST|PUT|DELETE|CONNECT|HEAD|OPTIONS|TRACE]
    url: '/user/12345',
    data: {
      firstName: 'Fred',
      lastName: 'Flintstone'
    },
    // #ifdef H5 || APP-PLUS || MP-ALIPAY || MP-WEIXIN
    timeout: 60000, // H5(HBuilderX 2.9.9+)、APP(HBuilderX 2.9.9+)、微信小程序（2.10.0）、支付宝小程序
    // #endif
    // params: { // 会拼接到url上
    //   token: '1111'
    // },
   
  })
}