@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=%~dp0\D:\newWork\uni-app-new\Framework\node_modules\.store\mqtt@3.0.0\node_modules"
) ELSE (
  @SET "NODE_PATH=%NODE_PATH%;%~dp0\D:\newWork\uni-app-new\Framework\node_modules\.store\mqtt@3.0.0\node_modules"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\.store\mqtt@3.0.0\node_modules\mqtt\bin\pub.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\.store\mqtt@3.0.0\node_modules\mqtt\bin\pub.js" %*
)
