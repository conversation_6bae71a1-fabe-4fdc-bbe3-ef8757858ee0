.zIndexMax {
	z-index: 9;
}
.zIndex9 {
	z-index: 9;
}
.cursor-pointer	{
    cursor: pointer;
}
.fontSize-28{
	font-size: 28rpx;
	
}
.fontSize-32{
	font-size: 32rpx;
	
}
 .fontSize-34{
 	font-size: 34rpx;
	
 }
// .fontSize-36{
// 	font-size: 36rpx;
	
// }
.fontWeight-bold{
	//font-weight: 400;
	font-weight: bold;
}
.middle {
    position: absolute;
    top: 50%; /* 将元素的顶部定位到父容器的中央位置 */
    left: 50%; /* 将元素的左侧定位到父容器的中央位置 */
    transform: translate(-50%, -50%); /* 通过负的margin值将元素的中心定位到父容器的中央位置 */
}
.middle-index{
	 position: absolute;
	    top: 5%; /* 将元素的顶部定位到父容器的中央位置 */
	    right: 5%; /* 将元素的左侧定位到父容器的中央位置 */
	    //transform: translate(-50%, -50%); /* 通过负的margin值将元素的中心定位到父容器的中央位置 */
}
.middle-right {
    position: absolute;
    top: 50%; /* 将元素的顶部定位到父容器的中央位置 */
    right: -10px; /* 将元素的左侧定位到父容器的中央位置 */
    transform: translate(-50%, -50%); /* 通过负的margin值将元素的中心定位到父容器的中央位置 */
}
.middle-middle {
    position: absolute;
    top: 20%; /* 将元素的顶部定位到父容器的中央位置 */
    right: 20%; /* 将元素的左侧定位到父容器的中央位置 */
    transform: translate(-50%, -50%); /* 通过负的margin值将元素的中心定位到父容器的中央位置 */
}
.middle-left {
    position: absolute;
    top: 50%; /* 将元素的顶部定位到父容器的中央位置 */
    left: -10px; /* 将元素的左侧定位到父容器的中央位置 */
    transform: translate(-50%, -50%); /* 通过负的margin值将元素的中心定位到父容器的中央位置 */
}
.static	{
    position: static;
}
.fixed	{
    position: fixed;
}
.absolute	{
    position: absolute;
}
.relative	{
    position: relative;
}
.sticky	{
    position: sticky;
}

.top-0	{
    top: 0px;
}
.top-20	{
    top: 20rpx;
}
.right-0	{
    right: 0px;
}
.bottom-0	{
    bottom: 0px;
}
.bottom-10	{
    bottom: 10px;
}
.left-0	{
    left: 0px;
}
.left-20	{
    left: 20rpx;
}
.w-full {
    width: 100%;
}
.w-screen	{
    width: 100vw;
}
.h-full{
    height: 100%;
}	
.h-screen{
    height: 100vh;
}	

.flex{
    display: flex;
}
.inline-flex{
    display: inline-flex;
}
.flex-row	{
    flex-direction: row;
}
.flex-row-reverse	{
    flex-direction: row-reverse;
}
.flex-col	{
    flex-direction: column;
}
.flex-col-reverse	{
    flex-direction: column-reverse;
}
.justify-start	{
    justify-content: flex-start;
}
.justify-end{
    justify-content: flex-end;
}	
.justify-center	{
    justify-content: center;
}
.justify-between{
    justify-content: space-between;
}	
.justify-around	{
    justify-content: space-around;
}
.justify-evenly{
    justify-content: space-evenly;
}

.items-start	{
    align-items: flex-start;
}
.items-end	{
    align-items: flex-end;
}
.items-center	{
    align-items: center;
}
.items-baseline	{
    align-items: baseline;
}
.items-stretch	{
    align-items: stretch;
}

.flex-wrap	{
    flex-wrap: wrap;
}
.flex-wrap-reverse	{
    flex-wrap: wrap-reverse;
}
.flex-nowrap	{
    flex-wrap: nowrap;
}


.flex-1{
    flex: 1 1 0%;
}
.flex-auto	{
    flex: 1 1 auto;
}
.flex-initial	{
    flex: 0 1 auto;
}
.flex-none	{
    flex: none;
}
.content-normal	{
    align-content: normal;
}
.content-center	{
    align-content: center;
}
.content-start	{
    align-content: flex-start;
}
.content-end	{
    align-content: flex-end;
}
.content-between	{
    align-content: space-between;
}
.content-around	{
    align-content: space-around;
}
.content-evenly	{
    align-content: space-evenly;
}
.content-baseline	{
    align-content: baseline;
}
.content-stretch	{
    align-content: stretch;
}
.static	{
    position: static;
}
.fixed	{
    position: fixed;
}
.absolute	{
    position: absolute;
}
.relative	{
    position: relative;
}
.sticky	{
    position: sticky;
}
.top-0	{
    top: 0px;
}
.right-0	{
    right: 0px;
}
.bottom-0	{
    bottom: 0px;
}
.left-0	{
    left: 0px;
}
.p-all-20{
	padding: 20rpx;
}
.p-all-40{
	padding: 40rpx;
}
.pr-20 {
	padding-left: 20rpx;
}
.pl-20 {
	padding-right: 20rpx;
}
.pb-20 {
	padding-bottom: 20rpx;
}
.bt-20 {
	padding-top: 20rpx;
}
.mt-20{
	  margin-top:20rpx;
}
.mt-40{
	  margin-top:40rpx;
}
.mt-80{
	  margin-top:80rpx;
}
.mb-20 {
	margin-bottom: 20rpx;
}
.mb-80 {
	margin-bottom: 80rpx;
}
.ml-20 {
	margin-left: 20rpx;
}
.mr-20  {
	margin-right: 20rpx;
}
.mt-auto {
    margin-top: auto;
}

//===========mt============
.overflow-auto	{
    overflow: auto;
}
.overflow-hidden	{
    overflow: hidden;
}
.overflow-visible	{
    overflow: visible;
}
.overflow-scroll	{
    overflow: scroll;
}
.overflow-x-auto	{
    overflow-x: auto;
}
.overflow-y-auto	{
    overflow-y: auto;
}
.overflow-x-hidden	{
    overflow-x: hidden;
}
.overflow-y-hidden	{
    overflow-y: hidden;
}
.overflow-x-visible	{
    overflow-x: visible;
}
.overflow-y-visible	{
    overflow-y: visible;
}
.overflow-x-scroll	{
    overflow-x: scroll;
}
.overflow-y-scroll	{
    overflow-y: scroll;
}

.border-gray{
	 border:1px solid gray;
}

.border-red	{
    border:1px solid red;
}
.border-blue {
    border:1px solid blue;
}
.bg-white	{
    background-color: #ffffff;
}

.paddingLR10{
    padding: 0px 10px;
    background-color: white;
}
.paddingLR20{
    padding: 0px 40rpx;
    background-color: white;
}
.marginLR10{
    margin: 0px 10px;
}
.bg-color-main{
	//background-color: #ebebeb;
	background-color: #efeff4;
}
.bg-image-bg{
	background-image: url(./static/images/bg/bg.png);
}
.bg-image-scan{
	background-image: url(./static/images/scan.png);
	background-size:60rpx 60rpx;
	background-repeat:no-repeat;
}
.bg-image-logo{
	background-image: url(./static/images/bg/logo.png);
	background-size:108rpx 108rpx;
	background-repeat:no-repeat;
}

.bg-image-userInfo{
	background-image: url(./static/images/logo/mrtx.png);
	background-size:128rpx 128rpx;
	background-repeat:no-repeat;
}


