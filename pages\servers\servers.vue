<template>
	<view class="flex flex-col bg-white justify-between">
		<view class="flex">
			<!-- top -->
		</view>
		<view style="padding: 10rpx;font-size: 32rpx;" class="flex-1 ">
			<view style="" class="formList">
				<view class="formItem flex items-center">
					<view style="width: 180rpx" class="formItemName flex justify-end">
						<text>扫描地址</text>
					</view>
					<view style="" class="formItemInput flex-1 w-full ">
						<uni-easyinput @keypress.enter="scanFullAddressEnter()" v-model="state.scanFullAddress"
							:inputBorder="false" :styles="{ 'backgroundColor': '#fff', 'width': '100%' }"
							placeholderStyle="fontSize:28rpx;" placeholder="请扫描二维码(非必填)"></uni-easyinput>
					</view>
					<view class="formItemIcon">
						<uni-icons @click="scanQRCode()" type="scan" color="#ccc" size="32"></uni-icons>
					</view>
				</view>
				<view class="formItem flex items-center">
					<view style="width: 180rpx" class="formItemName flex justify-end">
						<text>IP地址</text>
					</view>
					<view style="" class="formItemInput flex-1 w-full ">
						<uni-easyinput v-model="state.IPAddress" :inputBorder="false"
							:styles="{ 'backgroundColor': '#fff', 'width': '100%' }" placeholderStyle="fontSize:28rpx;"
							placeholder="请输入IP地址"></uni-easyinput>
					</view>
					<view class="formItemIcon">

					</view>
				</view>
				<view class="formItem flex items-center">
					<view style="width: 180rpx" class="formItemName flex justify-end">
						<text>端口号</text>
					</view>
					<view style="" class="formItemInput flex-1 w-full ">
						<uni-easyinput @change="checkBeforeLoadServe()" v-model="state.port" :inputBorder="false"
							:styles="{ 'backgroundColor': '#fff', 'width': '100%' }" placeholderStyle="fontSize:28rpx;"
							placeholder="请输入端口号"></uni-easyinput>
					</view>
					<view class="formItemIcon">

					</view>
				</view>

				<view @click="openPopup()" style="height: 35px;" @longpress="copyInfoFn()"
					class="formItem flex items-center">
					<view style="width: 180rpx" class="formItemName flex justify-end">
						<text>服务器</text>
					</view>
					<view style="margin-left: 10rpx;font-size: 13px;" class="formItemInput flex-1 w-full ">
						{{ state.currentServerName }}
					</view>
					<view class="formItemIcon ">
						<uni-icons type="forward" color="#ccc" size="28"></uni-icons>
					</view>
				</view>
				<view class="formItem flex items-center">
					<view style="width: 180rpx" class="formItemName flex justify-end">
						<!-- <text>看板</text> -->
					</view>
					<view style="" class="formItemInput flex-1 w-full ">
						<!-- <view style="height: 35px;" class="ml-20">
							<switch color="#4dd865" checked @change="switch1Change" />
						</view> -->
					</view>
					<view class="formItemIcon">

						<view style="height: 35px;" class="ml-20 flex items-center">
							<text class="mr-20">看板</text>
							<switch color="#4dd865" checked @change="switch1Change" />
						</view>
					</view>
				</view>
				<view style="height: 35px;" @longpress="copyInfoFn()" class="formItem flex items-center">
					<view style="width: 180rpx" class="formItemName flex justify-end">
						<text>设备ID</text>
					</view>
					<view style="" class="formItemInput flex-1 w-full ">
						<!-- <uni-easyinput :inputBorder="false"
							:styles="{'backgroundColor': '#fff','width':'100%'}" placeholderStyle="fontSize:28rpx;"
							placeholder=""></uni-easyinput> -->

					</view>
					<view style="margin-left: 10rpx;font-size: 13px;" class="formItemIcon mr-20">
						{{ state.deviceID }}
					</view>
				</view>
				<view style="height: 35px;" @longpress="copyInfoFn()" class="formItem flex items-center">
					<view style="width: 180rpx" class="formItemName flex justify-end">
						<text>序列号</text>
					</view>
					<view style="" class="formItemInput flex-1 w-full ">
						<!-- <uni-easyinput :inputBorder="false"
							:styles="{'backgroundColor': '#fff','width':'100%'}" placeholderStyle="fontSize:28rpx;"
							placeholder=""></uni-easyinput> -->

					</view>
					<view style="margin-left: 10rpx;font-size: 13px;" class="formItemIcon mr-20">
						{{ state.seqNo }}
					</view>
				</view>
			</view>

			<!-- 	弹框服务器滚动 可删除 列表 -->
			<uni-popup ref="popup" safe-area :mask-click="true" type="bottom" border-radius="10px 10px 0 0">

				<scroll-view :scroll-anchoring="true" style="background-color: #fff;" :scroll-top="scrollTop"
					scroll-y="true" class="scroll-Y relative" @scrolltoupper="upper" @scrolltolower="lower"
					@scroll="scroll">

					<uni-swipe-action>
						<uni-swipe-action-item v-for="(item, index) in state.serverList" :key="index"
							class="border-red1" :right-options="options" @click="onClick"
							@change="swipeChange($event, index)">
							<view @click="closePopup(item)"
								style="height: 100rpx;margin-left: 10rpx;border-bottom: 1px dashed #ccc;"
								class="flex h-full border-blue1 items-center">{{ item.CSERVER_NAME }}</view>
						</uni-swipe-action-item>
					</uni-swipe-action>

				</scroll-view>
			</uni-popup>
			<!-- 编辑服务内容弹框 -->
			<uni-popup ref="inputDialog" type="dialog">
				<uni-popup-dialog ref="inputClose" mode="input" title="编辑" value="提示内容!" placeholder="请输入服务名称"
					@confirm="dialogInputConfirm"></uni-popup-dialog>
			</uni-popup>
		</view>

		<view class="flex fixed bottom-10  w-full">
			<!-- bottom -->
			<!-- 	<view @tap="goTop" class="uni-link uni-center uni-common-mt">
							点击这里返回顶部
						</view> -->
			<button @click="saveServerData()" class="mb-20" style="width: 90%;background-color: #20273d;"
				type="primary">保存</button>
		</view>
	</view>
</template>

<script setup>
	import * as serviceApi from '@/api/index.js'
	import {
		getCurrentUserInfoByField
	} from '@/utils/tools.js'
	import {
		setStorageSync,
		getStorageSync,
		CURRENT_SERVER
	} from '@/utils/Storage.js'
	import {
		ref,
		reactive,
		onMounted
	} from 'vue'

	//components:{listWithBtns }

	///////////////status start/////////////////////
	const state = reactive({
		scanFullAddress: '',
		IPAddress: '',
		port: '',
		currentServerItem: null,
		currentServerName: '',
		serverList: [
			// { CID: '123', Name: '中洛PCB外网测试环境[************:6682]', Ip: '************', Port: '6682' },
			// { CID: '456', Name: '中洛PCB外网正式环境[************:6681]', Ip: '************', Port: '6681' }
		],
		deviceID: '1234567890',
		seqNo: 'xxx12-3456-7890',
		showKanBan: false,
	})
	const popup = ref(null)
	const inputDialog = ref(null) // 编辑服务弹框
	const isEdit = ref(false) // 是否正确编辑
	const scrollTop = ref(0)
	const old = ref({
		scrollTop: 0
	})
	const options = ref([{
		text: '编辑',
		style: {
			backgroundColor: '#007aff'
		}
	}, {
		text: '删除',
		style: {
			backgroundColor: '#dd524d'
		}
	}])
	const isClick = ref(true) // 判断是否是点击事件
	const Loop = ref(null)
	const hiddenBtnsWidth = ref(320) // 左滑默认宽度  注意更改时需要保持 .remove{width和right统一}
	const isScroll = ref(true) //是否有Y轴
	const startX = ref(0) //初始的X
	const noticeList = ref([{
			index: 0,
			right: 0,
			id: 1000
		}, {
			index: 1,
			right: 0,
			id: 1001
		}, {
			index: 2,
			right: 0,
			id: 1002
		}, {
			index: 3,
			right: 0,
			id: 1003
		}, {
			index: 4,
			right: 0,
			id: 1004
		}, {
			index: 5,
			right: 0,
			id: 1005
		}, {
			index: 6,
			right: 0,
			id: 1006
		},
		{
			index: 7,
			right: 0,
			id: 1007
		},
		{
			index: 8,
			right: 0,
			id: 1008
		},
		{
			index: 9,
			right: 0,
			id: 1009
		},
	])
	///////////////status start/////////////////////

	///////////////methods start/////////////////////
	onMounted(() => {
		let _DeviceInfo = uni.getDeviceInfo()
		//console.log('DeviceInfo:',DeviceInfo)
		state.deviceID = _DeviceInfo.deviceId
		getServerData()
	})
	
	//  返回配置信息（登录页、首页、关于 APP 配置）
	async function loadConfig() {
		let params = {
			languate: 'zh',
			userName: 'xxc'
		}
		let resData = null
		await serviceApi.config(params).then(res => {
			if (res && res.data.code === 200 && res.data.data.Success) {
				// 保存服务列表
				setStorageSync("LOGIN_CONFIG", res.data.data.Datas)
				resData = res.data.data.Datas
			}
		}).catch(err => {
			console.log('返回服务列表及本机数字:', err)
		})
		return resData
	}

	function checkBeforeLoadServe() {
		if (!state.IPAddress) {
			uni.showToast({
				title: 'IP不能为空',
				icon: 'none'
			})
			return
		}
		if (!state.port) {
			uni.showToast({
				title: '端口不能为空',
				icon: 'none'
			})
			return
		}
		if (!!state.IPAddress && !!state.port) {
			loadServerList()
		}

	}
	// 加载菜单数据
	function loadServerList() {
		const params = {
			mac: state.deviceID, //getCurrentUserInfoByField('CUSER_NAME'),
			systemtype: 'APP'
		}
		uni.showLoading({
			title: '加载中'
		});
		serviceApi.server(null, params).then(res => {
			if (res && res.data.code === 200 && res.data.data.Success) {
				//  "CSERVER_NAME": "誉信电子",
				// "CMIDDLEWARE": "http://************:6672/api/",
				// "CWEB_URL": "",
				// "CDESC": "",
				// "CMSG_SEND_SERVER": "",
				// "CMSG_ACCEPT_SERVER": "",
				// "IS_READONLY": true,
				// "IS_UPDATE": false,
				// "CID": "202221218906182",
				// "CUSER_CREATED": "SYS",
				// "CDATETIME_CREATED": "2024-07-25 18:00:14",
				// "CUSER_MODIFIED": "SYS",
				// "CDATETIME_MODIFIED": "2024-07-25 18:00:14",
				// "CSTATE": "A",
				// "CSTATE_BOOL": true,
				// "CINSTANCE_ID": "",
				// "CROWREMARK": "",
				// "CENTERPRISE_CODE": "100",
				// "CORG_CODE": "0"
				state.serverList = res.data.data.Datas.Servers
				setStorageSync("SERVER_LIST", state.serverList)
			} else {
				uni.showToast({
					title: res && res.data.data.Content ? res.data.data.Content : '获取服务列表失败',
					icon: 'none'
				})
			}
		}).catch(err => {
			uni.showToast({
				title: '网络异常，请重试',
				icon: 'none'
			})
		}).finally(() => {
			uni.hideLoading();
		})
	}
	// 是否显示看板
	function switch1Change(val) {
		setStorageSync("SHOW_KANBAN", val)
	}

	function getServerData() {
		let data = getStorageSync(CURRENT_SERVER)
		if (data && data.CSERVER_NAME) {
			state.currentServerName = data.CSERVER_NAME
		}
		let _servers = getStorageSync("SERVER_LIST")
		//debugger
		if (_servers) {
			state.serverList = _servers
		}
	}
	async function saveServerData() {
		let resConfig = await loadConfig()
		setStorageSync(CURRENT_SERVER, state.currentServerItem)
		//debugger
		reLaunch('/pages/login/login')
	}

	function scanFullAddressEnter() {
		if (state.scanFullAddress) {
			if (state.scanFullAddress.includes(':')) {
				let dataArr = state.scanFullAddress.split(':')
				if (dataArr && dataArr.length > 1) {
					state.IPAddress = dataArr[0]
					state.port = dataArr[1]
					checkBeforeLoadServe()
				}
			} else {
				state.IPAddress = state.scanFullAddress
			}
			setTimeout(() => {
				state.scanFullAddress = ""
			}, 300)
		}
	}

	function scanQRCode() {
		// 允许从相机和相册扫码
		uni.scanCode({
			success: function(res) {
				// console.log('条码类型：' + res.scanType);
				// console.log('条码内容：' + res.result);
				state.scanFullAddress = res.result
				scanFullAddressEnter()
			}
		});
	}

	function onClick(e) {
		//debugger
		console.log('点击了' + (e.position === 'left' ? '左侧' : '右侧') + e.content.text + '按钮')
	}

	function change(event) {
		console.log('改变事件', event);
	}

	function swipeChange(e, index) {
		//debugger
		console.log('当前状态：' + e + '，下标：' + index)
		//editServerItem(item,$event)
	}

	function reLaunch(url) {
		uni.reLaunch({
			url: url
		})
	}

	function selectServiceItemFn(val) {
		if (!isEdit.value) {
			// 是否正在编辑
			closePopup()
		}

	}

	function copyInfoFn() {
		uni.setClipboardData({
			data: '1234567890',
			//showToast:false,
			success: function() {
				// uni.showToast({
				// 	title:'复制成功！'
				// })
			}
		});
	}
	// 目前uni-app view标签不支持双击事件，下面自定义双击事件
	function copyInfo() {
		let _self = this;
		let curTime = new Date().getTime();
		let lastTime = _self.lastTapDiffTime;
		_self.lastTapDiffTime = curTime;
		//两次点击间隔小于300ms, 认为是双击
		let diff = curTime - lastTime;
		if (diff < 300) {
			console.log("双击")
			//_self.handleVideo('screen',index)自定义事件
			uni.setClipboardData({
				data: '1234567890',
				//showToast:false,
				success: function() {
					// uni.showToast({
					// 	title:'复制成功！'
					// })
				}
			});
			clearTimeout(_self.lastTapTimeoutFunc); // 成功触发双击事件时，取消单击事件的执行
		} else {
			// 单击事件延时300毫秒执行
			_self.lastTapTimeoutFunc = setTimeout(function() {
				console.log("单击")
				//_self.handleVideo('playOrStop',index)自定义事件
			}, 300);
		}

	}

	function editServerItem(item, event) {
		openEidtServer()
		console.log("编辑", event)
		//event.stopPropagation();
	}

	function delServerItem(item, event) {
		//debugger
		isClick.value = true
		console.log("删除", event)
		//event.stopPropagation();
	}

	function drawStart(e) {
		console.log("开始触发");
		let touch = e.touches[0];
		startX.value = touch.clientX;

	}

	function resetListRight() {
		for (let index in noticeList.value) {
			noticeList.value[index].right = 0;
		} //保证滑动前为0，和其它已滑动的为0
		isEdit.value = false // 是否正在编辑
	}

	function drawMove(e) {
		resetListRight()
		//获取从点击到滑动离开鼠标X轴的距离，大于20将滑动的值赋为hiddenBtnsWidth(160),否则为0
		//console.log("移动", e)
		isEdit.value = false // 是否正在编辑
		let touch = e.touches[0]
		let item = noticeList.value[e.currentTarget.dataset.index]
		let disX = startX.value - touch.clientX
		//console.log("移动disX", disX)
		if (disX >= 20) {
			isEdit.value = true // 是否正在编辑
			if (disX > hiddenBtnsWidth.value) {
				disX = hiddenBtnsWidth.value;
			}
			isScroll.value = false;
			noticeList.value[e.currentTarget.dataset.index].right = disX;
		} else {

			isScroll.value = true;
			noticeList.value[e.currentTarget.dataset.index].right = 0;
		}
	}

	function drawEnd(e) {
		console.log("结束", e)
		let item = noticeList.value[e.currentTarget.dataset.index];
		// 下面代码同来判断是左滑还是右滑
		if (item.right >= hiddenBtnsWidth.value / 2) {
			isScroll.value = true;
			noticeList.value[e.currentTarget.dataset.index].right = hiddenBtnsWidth.value;
		} else {
			isScroll.value = true;
			noticeList.value[e.currentTarget.dataset.index].right = 0;
		}
	}
	//删除消息
	function delItem() {
		console.log('删除', item.id);
	}

	function openEidtServer() {
		inputDialog.value.open()
	}

	function closeEidtServer() {
		inputDialog.value.close()
	}

	function dialogInputConfirm() {
		uni.showToast({
			title: '修改成功！',
			duration: 2000
		});
		//resetListRight()
	}

	function openPopup() {
		//resetListRight()
		popup.value.open()

	}

	function closePopup(item) {
		state.currentServerItem = item
		state.currentServerName = item.CSERVER_NAME

		popup.value.close()
	}

	function upper(e) {

	}

	function lower(e) {

	}

	function scroll(e) {
		scrollTop.value = e.detail.scrollTop
	}

	function goTop(e) {
		// 解决view层不同步的问题
		scrollTop.value = old.value.scrollTop
		setTimeout(() => {
			scrollTop.value = 0
		})
		uni.showToast({
			icon: "none",
			title: "纵向滚动 scrollTop 值已被修改为 0"
		})
	}
	///////////////methods end/////////////////////
</script>

<style lang="scss" scoped>
	.server-list-item {
		width: 100%;
		display: flex;
		align-items: center;
		position: relative;
		background-color: #FFFFFF;
		transition: all 0.2s;
	}

	.hiddenBtn {
		width: 320rpx;
		height: 100%;
		// background-color: red;
		color: white;
		position: absolute;
		top: 0;
		right: -320rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 30rpx;
		z-index: 99999;
	}

	.formItem {
		border-bottom: 1px solid #e9e9e9;
		padding: 20rpx 0rpx;
	}

	.scroll-Y {
		height: 800rpx;
	}
</style>