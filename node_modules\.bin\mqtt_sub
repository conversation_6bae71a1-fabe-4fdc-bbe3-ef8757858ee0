#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="$basedir/D:/newWork/uni-app-new/Framework/node_modules/.store/mqtt@3.0.0/node_modules"
else
  export NODE_PATH="$NODE_PATH:$basedir/D:/newWork/uni-app-new/Framework/node_modules/.store/mqtt@3.0.0/node_modules"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.store/mqtt@3.0.0/node_modules/mqtt/bin/sub.js" "$@"
else
  exec node  "$basedir/../.store/mqtt@3.0.0/node_modules/mqtt/bin/sub.js" "$@"
fi
