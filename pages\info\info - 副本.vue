<template>
	<view class="bg-color-main1">
		<uni-nav-bar status-bar :fixed="true" :border="false">
			<view class="flex fontSize-34  w-full  justify-center items-center">消息</view>
			<block v-slot:right>
				<uni-icons @click="toggleMenu()" type="more-filled" size="30"></uni-icons>

			</block>
		</uni-nav-bar>
		<!--注释-->
		 <!-- 下拉刷新区域 -->
		
		
			<view :style="{height: `${windowBodyHeight}px`}" class="flex overflow-hidden border-blue bg-white w-full">
				<view class="flex justify-between  w-full flex-col">
				<!-- 分类 -->
				<u-sticky style="background-color: #efeff4;" class="p-all-20  " offsetTop="0">
					<uni-easyinput suffixIcon="search" placeholder="搜索消息" @iconClick="iconClick"></uni-easyinput>
				</u-sticky>
				<!-- 列表内容 -->
				 
				<view  class=" flex-1 overflow-y-auto flex flex-col pl-20 pr-20 ">
					<block v-if="messageList">
						<uni-list class="mb-80">
							<uni-list :border="true">
							
								<uni-list-chat title="第一条数据" avatar="https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/unicloudlogo.png" note="您收到一条新的消息" time="2020-02-02 20:20" badge-text="12"></uni-list-chat>
								<!-- 头像显示圆点 -->
								<uni-list-chat title="uni-app" avatar="https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/unicloudlogo.png" note="您收到一条新的消息" time="2020-02-02 20:20" badge-positon="left" badge-text="dot"></uni-list-chat>
								<!-- 头像显示角标 -->
								<uni-list-chat title="uni-app" avatar="https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/unicloudlogo.png" note="您收到一条新的消息" time="2020-02-02 20:20" badge-positon="left" badge-text="99"></uni-list-chat>
								<!-- 显示多头像 -->
								<uni-list-chat title="uni-app" :avatar-list="avatarList" note="您收到一条新的消息" time="2020-02-02 20:20" badge-positon="left" badge-text="dot"></uni-list-chat>
								<!-- 自定义右侧内容 -->
								<uni-list-chat title="uni-app" :avatar-list="avatarList" note="您收到一条新的消息" time="2020-02-02 20:20" badge-positon="left" badge-text="dot">
									<view class="chat-custom-right">
										<text class="chat-custom-text">刚刚</text>
										<!-- 需要使用 uni-icons 请自行引入 -->
										<uni-icons type="star-filled" color="#999" size="18"></uni-icons>
									</view>
								</uni-list-chat>
								<!-- 显示圆形头像 -->
								<uni-list-chat :avatar-circle="true" title="uni-app" avatar="https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/unicloudlogo.png" note="您收到一条新的消息" time="2020-02-02 20:20" ></uni-list-chat>
								<!-- 右侧带角标 -->
								<uni-list-chat title="uni-app" avatar="https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/unicloudlogo.png" note="您收到一条新的消息" time="2020-02-02 20:20" badge-text="12"></uni-list-chat>
								<!-- 头像显示圆点 -->
								<uni-list-chat title="uni-app" avatar="https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/unicloudlogo.png" note="您收到一条新的消息" time="2020-02-02 20:20" badge-positon="left" badge-text="dot"></uni-list-chat>
								<!-- 头像显示角标 -->
								<uni-list-chat title="uni-app" avatar="https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/unicloudlogo.png" note="您收到一条新的消息" time="2020-02-02 20:20" badge-positon="left" badge-text="99"></uni-list-chat>
								<!-- 显示多头像 -->
								<uni-list-chat title="uni-app" :avatar-list="avatarList" note="您收到一条新的消息" time="2020-02-02 20:20" badge-positon="left" badge-text="dot"></uni-list-chat>
								<!-- 自定义右侧内容 -->
								<uni-list-chat title="uni-app" :avatar-list="avatarList" note="您收到一条新的消息" time="2020-02-02 20:20" badge-positon="left" badge-text="dot">
									<view class="chat-custom-right">
										<text class="chat-custom-text">刚刚</text>
										<!-- 需要使用 uni-icons 请自行引入 -->
										<uni-icons type="star-filled" color="#999" size="18"></uni-icons>
									</view>
								</uni-list-chat>
								<!-- 显示圆形头像 -->
								<uni-list-chat :avatar-circle="true" title="uni-app" avatar="https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/unicloudlogo.png" note="您收到一条新的消息" time="2020-02-02 20:20" ></uni-list-chat>
								<!-- 右侧带角标 -->
								<uni-list-chat title="uni-app" avatar="https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/unicloudlogo.png" note="您收到一条新的消息" time="2020-02-02 20:20" badge-text="12"></uni-list-chat>
								<!-- 头像显示圆点 -->
								<uni-list-chat title="uni-app" avatar="https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/unicloudlogo.png" note="您收到一条新的消息" time="2020-02-02 20:20" badge-positon="left" badge-text="dot"></uni-list-chat>
								<!-- 头像显示角标 -->
								<uni-list-chat title="uni-app" avatar="https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/unicloudlogo.png" note="您收到一条新的消息" time="2020-02-02 20:20" badge-positon="left" badge-text="99"></uni-list-chat>
								<!-- 显示多头像 -->
								<uni-list-chat title="uni-app" :avatar-list="avatarList" note="您收到一条新的消息" time="2020-02-02 20:20" badge-positon="left" badge-text="dot"></uni-list-chat>
								<!-- 自定义右侧内容 -->
								<uni-list-chat title="uni-app" :avatar-list="avatarList" note="您收到一条新的消息" time="2020-02-02 20:20" badge-positon="left" badge-text="dot">
									<view class="chat-custom-right">
										<text class="chat-custom-text">刚刚</text>
										<!-- 需要使用 uni-icons 请自行引入 -->
										<uni-icons type="star-filled" color="#999" size="18"></uni-icons>
									</view>
								</uni-list-chat>
								<!-- 显示圆形头像 -->
								<uni-list-chat :avatar-circle="true" title="uni-app" avatar="https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/unicloudlogo.png" note="您收到一条新的消息" time="2020-02-02 20:20" ></uni-list-chat>
								<!-- 右侧带角标 -->
								<uni-list-chat title="uni-app" avatar="https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/unicloudlogo.png" note="您收到一条新的消息" time="2020-02-02 20:20" badge-text="12"></uni-list-chat>
								<!-- 头像显示圆点 -->
								<uni-list-chat title="uni-app" avatar="https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/unicloudlogo.png" note="您收到一条新的消息" time="2020-02-02 20:20" badge-positon="left" badge-text="dot"></uni-list-chat>
								<!-- 头像显示角标 -->
								<uni-list-chat title="uni-app" avatar="https://qiniu-web-assets.dcloud.net.cn/unidoc/zh/unicloudlogo.png" note="您收到一条新的消息" time="2020-02-02 20:20" badge-positon="left" badge-text="99"></uni-list-chat>
								<!-- 显示多头像 -->
								<uni-list-chat title="uni-app" :avatar-list="avatarList" note="您收到一条新的消息" time="2020-02-02 20:20" badge-positon="left" badge-text="dot"></uni-list-chat>
								<!-- 自定义右侧内容 -->
								<uni-list-chat title="最后一条" :avatar-list="avatarList" note="您收到一条新的消息" time="2020-02-02 20:20" badge-positon="left" badge-text="dot">
									<view class="chat-custom-right">
										<text class="chat-custom-text">刚刚-最后一条</text>
										<!-- 需要使用 uni-icons 请自行引入 -->
										<uni-icons type="star-filled" color="#999" size="18"></uni-icons>
									</view>
								</uni-list-chat>
							</uni-list>
						</uni-list>
					</block>
					<block v-else>
						<view class="flex justify-center items-center h-full ">
							暂无数据
						</view>
						
					</block>
					
					
				</view>
			<!-- 	<scroll-view scroll-y="true" class="flex-1 overflow-y-auto"> </scroll-view> -->
				
				
			</view>
			</view>
		<!--  -->
	</view>
</template>

<script setup>
	// http://**************:5173/#/demo
	import {
		computed,
		ref,
		reactive
	} from 'vue';
	//import { onPullDownRefresh, stopPullDownRefresh } from '@dcloudio/uni-app';
	import { onReady,onPullDownRefresh } from '@dcloudio/uni-app'
	const pulldownHeight = ref(60);
	const pulldownState = ref('normal'); // pulling, loosing, refreshing
	
	const messageList = ref(99)
	const currentTab = ref('read')
	const toggleType = ref(false) // 顶部弹框是否打开
	const popup_top = ref(null) // 弹框实例
	const navBarHeight = ref(44) // 顶部标题高度
	const tabBarHeight = ref(50) // 底部菜单栏高度	
	const systemInfo = uni.getSystemInfoSync(); //系统信息
	const windowHeight = computed(() => {
		//windowHeight不包含NavigationBar和TabBar的高度
		return systemInfo.windowHeight
	})
	const windowBodyHeight = computed(() => {
		//windowHeight不包含NavigationBar和TabBar的高度
		return systemInfo.windowHeight - navBarHeight.value - systemInfo.safeArea.top 
	})
	const webviewStyles = ref({
		zIndex: -111111,
		top: navBarHeight.value + systemInfo.safeArea.top + 'px',
		width: '100%',
		height: windowHeight.value - navBarHeight.value - systemInfo.safeArea.top + 'px',
		progress: {
			color: '#FF3333'
		}
	})
	const pattern = ref({
		color: '#7A7E83',
		backgroundColor: '#fff',
		selectedColor: '#007AFF',
		buttonColor: '#007AFF',
		iconColor: '#fff'
	})
	// const content = ref([{
	// 		iconPath: '/static/image.png',
	// 		selectedIconPath: '/static/image-active.png',
	// 		text: '相册',
	// 		active: false
	// 	},
	// 	{
	// 		iconPath: '/static/home.png',
	// 		selectedIconPath: '/static/home-active.png',
	// 		text: '首页',
	// 		active: false
	// 	},
	// 	{
	// 		iconPath: '/static/star.png',
	// 		selectedIconPath: '/static/star-active.png',
	// 		text: '收藏',
	// 		active: false
	// 	}
	// ])
	/////////////////////////内置方法/////////////////////////
	// 监听下拉状态变化
	const onStateChange = (e) => {
	  pulldownState.value = e.detail.state;
	};
	onPullDownRefresh(() => {
	  // 模拟数据更新
	  console.log('====onPullDownRefresh====');
	  setTimeout(() => {
	   // uni.stopPullDownRefresh();
		uni.stopPullDownRefresh({
				// success: function () {
				// 	uni.showToast({
				// 		title:'刷新成功！'
				// 	})
				// },
				// fail: function () {
				// 	uni.showToast({
				// 		title:'刷新失败！'
				// 	})
				// },
				// complete: function () {
				// 	uni.showToast({
				// 		title:'刷新完成！'
				// 	})
				// }
		});
	  }, 1000);
	})

	//////////////////////methods///////////////////////////
	function loadMessageList(){
		companyType.value =[]
		const params = {}
		 		
		serviceApi.notice(null,params).then(res => {
			if (res && res.data.code === 200 && res.data.data) {

			} else {
				uni.showToast({
					title: res && res.data.data.Content ? res.data.data.Content : '获取消息列表失败',
					icon: 'none'
				})
			}
		}).catch(err => {
			uni.showToast({
				title: '服务异常，请检查配置!',
				icon: 'none'
			})
		})
	}
		function iconClick(){
			
		}
   async function currentTabFn(type='read'){
		currentTab.value = type
		if(type=='read'){
			messageList.value = 50
		}else{
			messageList.value = 0
		}
		
	}
   
	function toggleMenu(type) {
		uni.showActionSheet({
			itemList: ['全部已读', '全部删除'],
			success: function(res) {
				console.log('选中了第' + (res.tapIndex + 1) + '个按钮');
			},
			fail: function(res) {
				console.log(res.errMsg);
			}
		});
	}

	function clickRightEvent() {
		uni.showToast({
			title: 'clickRightEvent'
		})
	}

	function checkUrl(url) {
		return fetch(url, {
				method: 'HEAD',
				mode: 'no-cors'
			})
			.then(response => {
				if (response.ok) {
					console.log(url, 'is accessible');
					return true;
				} else {
					console.log(url, 'is not accessible');
					return false;
				}
			})
			.catch(error => {
				console.error(error);
				console.log(url, 'is not accessible');
				return false;
			});
	}
	//////////////////////methods///////////////////////////
</script>

<style lang="scss" scoped>
	
</style>