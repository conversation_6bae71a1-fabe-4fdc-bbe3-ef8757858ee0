import {
  __commonJS
} from "./chunk-TDUMLE5V.js";

// ../../../../../newWork/uni-app-new/Framework/node_modules/lodash/isObject.js
var require_isObject = __commonJS({
  "../../../../../newWork/uni-app-new/Framework/node_modules/lodash/isObject.js"(exports, module) {
    function isObject(value) {
      var type = typeof value;
      return value != null && (type == "object" || type == "function");
    }
    module.exports = isObject;
  }
});

// ../../../../../newWork/uni-app-new/Framework/node_modules/lodash/_freeGlobal.js
var require_freeGlobal = __commonJS({
  "../../../../../newWork/uni-app-new/Framework/node_modules/lodash/_freeGlobal.js"(exports, module) {
    var freeGlobal = typeof global == "object" && global && global.Object === Object && global;
    module.exports = freeGlobal;
  }
});

// ../../../../../newWork/uni-app-new/Framework/node_modules/lodash/_root.js
var require_root = __commonJS({
  "../../../../../newWork/uni-app-new/Framework/node_modules/lodash/_root.js"(exports, module) {
    var freeGlobal = require_freeGlobal();
    var freeSelf = typeof self == "object" && self && self.Object === Object && self;
    var root = freeGlobal || freeSelf || Function("return this")();
    module.exports = root;
  }
});

// ../../../../../newWork/uni-app-new/Framework/node_modules/lodash/_Symbol.js
var require_Symbol = __commonJS({
  "../../../../../newWork/uni-app-new/Framework/node_modules/lodash/_Symbol.js"(exports, module) {
    var root = require_root();
    var Symbol = root.Symbol;
    module.exports = Symbol;
  }
});

// ../../../../../newWork/uni-app-new/Framework/node_modules/lodash/_getRawTag.js
var require_getRawTag = __commonJS({
  "../../../../../newWork/uni-app-new/Framework/node_modules/lodash/_getRawTag.js"(exports, module) {
    var Symbol = require_Symbol();
    var objectProto = Object.prototype;
    var hasOwnProperty = objectProto.hasOwnProperty;
    var nativeObjectToString = objectProto.toString;
    var symToStringTag = Symbol ? Symbol.toStringTag : void 0;
    function getRawTag(value) {
      var isOwn = hasOwnProperty.call(value, symToStringTag), tag = value[symToStringTag];
      try {
        value[symToStringTag] = void 0;
        var unmasked = true;
      } catch (e) {
      }
      var result = nativeObjectToString.call(value);
      if (unmasked) {
        if (isOwn) {
          value[symToStringTag] = tag;
        } else {
          delete value[symToStringTag];
        }
      }
      return result;
    }
    module.exports = getRawTag;
  }
});

// ../../../../../newWork/uni-app-new/Framework/node_modules/lodash/_objectToString.js
var require_objectToString = __commonJS({
  "../../../../../newWork/uni-app-new/Framework/node_modules/lodash/_objectToString.js"(exports, module) {
    var objectProto = Object.prototype;
    var nativeObjectToString = objectProto.toString;
    function objectToString(value) {
      return nativeObjectToString.call(value);
    }
    module.exports = objectToString;
  }
});

// ../../../../../newWork/uni-app-new/Framework/node_modules/lodash/_baseGetTag.js
var require_baseGetTag = __commonJS({
  "../../../../../newWork/uni-app-new/Framework/node_modules/lodash/_baseGetTag.js"(exports, module) {
    var Symbol = require_Symbol();
    var getRawTag = require_getRawTag();
    var objectToString = require_objectToString();
    var nullTag = "[object Null]";
    var undefinedTag = "[object Undefined]";
    var symToStringTag = Symbol ? Symbol.toStringTag : void 0;
    function baseGetTag(value) {
      if (value == null) {
        return value === void 0 ? undefinedTag : nullTag;
      }
      return symToStringTag && symToStringTag in Object(value) ? getRawTag(value) : objectToString(value);
    }
    module.exports = baseGetTag;
  }
});

// ../../../../../newWork/uni-app-new/Framework/node_modules/lodash/isObjectLike.js
var require_isObjectLike = __commonJS({
  "../../../../../newWork/uni-app-new/Framework/node_modules/lodash/isObjectLike.js"(exports, module) {
    function isObjectLike(value) {
      return value != null && typeof value == "object";
    }
    module.exports = isObjectLike;
  }
});

// ../../../../../newWork/uni-app-new/Framework/node_modules/lodash/isSymbol.js
var require_isSymbol = __commonJS({
  "../../../../../newWork/uni-app-new/Framework/node_modules/lodash/isSymbol.js"(exports, module) {
    var baseGetTag = require_baseGetTag();
    var isObjectLike = require_isObjectLike();
    var symbolTag = "[object Symbol]";
    function isSymbol(value) {
      return typeof value == "symbol" || isObjectLike(value) && baseGetTag(value) == symbolTag;
    }
    module.exports = isSymbol;
  }
});

export {
  require_freeGlobal,
  require_root,
  require_Symbol,
  require_baseGetTag,
  require_isObject,
  require_isObjectLike,
  require_isSymbol
};
//# sourceMappingURL=chunk-QJCCXIUL.js.map
